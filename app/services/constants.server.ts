import { momentTz } from 'btaskee-utils';
import { Types } from 'mongo-connection';

export const statusOriginal = {
    ACTIVE: 'ACTIVE',
    REMOVED: 'REMOVED',
    INACTIVE: 'INACTIVE',
};

export const newRecordCommonField = () => ({
    createdAt: momentTz().toDate(),
    _id: new Types.ObjectId().toString(),
    status: statusOriginal.ACTIVE,
});

export const EXPIRED_RESET_PASSWORD = 15; // by minutes
export const EXPIRED_VERIFICATION_CODE = 10; // by minutes

export enum TASKER_PROFILE_STATUS {
    VERIFYING = 'VERIFYING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    ELIMINATED = 'ELIMINATED',
    NEEDS_UPDATE = 'NEEDS_UPDATE',
    FAIL_UPDATED = 'FAIL_UPDATED',
    UPDATED = 'UPDATED',
}

export enum TASKER_ONBOARDING_PROCESS_STATUS {
    RESTORED = 'RESTORED',
    RESCHEDULE = 'RESCHEDULE',
    ACTIVE = 'ACTIVE',
    FAIL_INTERVIEW = 'FAIL_INTERVIEW',
    FAIL_CALLING = 'FAIL_CALLING',
    VERIFYING = 'VERIFYING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    SCHEDULED = 'SCHEDULED',
    ELIMINATED = 'ELIMINATED',
    NEEDS_UPDATE = 'NEEDS_UPDATE',
    FAIL_UPDATED = 'FAIL_UPDATED',
    UPDATED = 'UPDATED',
}

export const NOTIFICATION_TYPE_FROM_BACKEND = {
    TO_ASKER_APP: 18,
    DEFAULT: 25,
};

export enum TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API {
    IDENTITY_CARD = 'identityCardStatus',
    HOUSEHOLD = 'householdStatus',
    CRIMINAL_RECORD = 'criminalRecordsStatus',
    MASSAGE_PRACTICE_CERTIFICATE = 'massagePracticeCertificate',
    PORTRAIT = 'portraitStatus',
    PASSPORT = 'passportStatus',
    HOUSE_ELECTRIC_BILL = 'houseElectricBillStatus',
    WORK_PERMIT = 'workPermitStatus',
}

export enum USER_TYPE {
    TASKER = 'TASKER',
    ASKER = 'ASKER',
}

export enum TASK_STATUS {
    WAITING_ASKER_CONFIRMATION = 'WAITING_ASKER_CONFIRMATION',
    CONFIRMED = 'CONFIRMED',
    DONE = 'DONE',
    POSTED = 'POSTED',
    CANCELED = 'CANCELED',
    EXPIRED = 'EXPIRED',
}

export enum SERVICE_TEXT_IN_REPORT_TASKER_OPERATION {
    ALL_SERVICE = 'All Service',
    PREMIUM_SERVICE = 'Premium Service',
}

export enum USER_STATUS {
    ACTIVE = 'ACTIVE',
    DISABLED = 'DISABLED',
    INACTIVE = 'INACTIVE',
    IN_PROBATION = 'IN_PROBATION',
    LOCKED = 'LOCKED',
    UNVERIFIED = 'UNVERIFIED',
    UNLOCKED = 'UNLOCKED',
    PASSED_INTERVIEW = 'PASSED_INTERVIEW',
}

export enum FILTER_OPTION_IN_QUIZ_COLLECTION {
    IMAGE_ONLY = 'IMAGE_ONLY',
    VIDEO_ONLY = 'VIDEO_ONLY',
    NO_MEDIA = 'NO_MEDIA',
}

export enum FILTER_OPTION_IN_QUIZ {
    YES = 'YES',
    NO = 'NO',
}

export enum TASKER_ONBOARDING_IMAGE_KEY {
    IDENTITY_CARD = 'identityCard',
    HOUSEHOLD = 'household',
    CRIMINAL_RECORD = 'criminalRecords',
    PORTRAIT = 'portrait',
    CONFIRMATION_CONDUCT = 'confirmationConduct',
    CURRICULUM_VITAE = 'confirmationConduct',
    MASSAGE_PRACTICE_CERTIFICATE = 'massagePracticeCertificate',
    PASSPORT = 'passport',
    HOUSE_ELECTRIC_BILL = 'houseElectricBill',
    WORK_PERMIT = 'workPermit',
}

export enum REASON_TYPE_ON_TASKER_ONBOARDING_CONFIGURATION {
    REJECTED = 'REJECTED',
    ELIMINATED = 'ELIMINATED',
    FAIL_ELIMINATED = 'FAIL_ELIMINATED',
    FAIL_CALLING = 'FAIL_CALLING',
    FAIL_INTERVIEW = 'FAIL_INTERVIEW',
}

export const EARTH_RADIUS = 6371000; // Radius of the Earth in meters

export const COMMON_MIN_SEARCH_LENGTH = 3;

// Identity Verification Status Constants
export enum IDENTITY_VERIFICATION_STATUS {
    NOT_UPLOADED = 'NOT_UPLOADED',
    VERIFYING = 'VERIFYING',
    APPROVED = 'APPROVED',
    NEEDS_UPDATE = 'NEEDS_UPDATE',
    UPDATED = 'UPDATED',
}
