import type { ConfirmUpdatingProfileFormProps } from 'app/components/tasker-common';
import { res404 } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import type { EnumIsoCode } from 'schemas';
import { getModels } from 'schemas';
import getRestApiByMultiRegion from '~/services/api-proxy/index.server';
import {
    IDENTITY_VERIFICATION_STATUS,
    TASKER_ONBOARDING_PROCESS_STATUS,
    TASKER_PROFILE_STATUS,
} from '~/services/constants.server';
import { employeeProfile } from '~/services/model/decorator/employeeProfile';
import { taskerProfile } from '~/services/model/decorator/taskerProfile';
import UsersModel from '~/services/model/users.server';
import {
    fetchAPI,
    getImageStatusKeyByImageFieldInTaskerOnboarding,
    sendNotification,
} from '~/services/utils.server';

export type GettingProfileProps = {
    status: Array<TASKER_ONBOARDING_PROCESS_STATUS>;
    createdAt: { from: Date; to: Date };
    searchText: ReturnType<URLSearchParams['toString']>;
    isPartner?: TaskerProfile['isPartner'];
    cities: string[];
    isoCode: EnumIsoCode;
};

export type GettingProfileWithPagination = GettingProfileProps & {
    sort: PipelineStage.Sort['$sort'];
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
};

export type UpdatingTaskerProfileProps = {
    isoCode: EnumIsoCode;
    profileId: TaskerProfile['_id'];
    fieldName?: `${TASKER_ONBOARDING_IMAGE_KEY}`;
    status: `${TASKER_ONBOARDING_PROCESS_STATUS}`;
    reason?: ConfirmUpdatingProfileFormProps['reason'];
    userId: Users['_id'];
    language?: BtaskeeLanguage;
    username: Users['username'];
    message?: {
        title: TextLang;
        body: TextLang;
    };
    isPartner?: TaskerProfile['isPartner'];
};

export type ReturnResponseFromQuery = {
    services: Array<Pick<Service, 'text' | 'isSubscription' | 'icon' | 'name'>>;
    taskerGender: UserApp['gender'];
    taskerLanguage: UserApp['language'];
    taskerWorkingPlaces: UserApp['workingPlaces'];
    username: Users['username'];
};

function getOverallStatusInProfileToServer({
    processStatus,
    status,
    isHaveAppointmentInfo,
}: {
    processStatus?: TaskerProfile['processStatus'];
    status?: TaskerProfile['status'];
    isHaveAppointmentInfo: boolean;
}) {
    if (!status && !processStatus) {
        return TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING;
    }

    /**
     * @param processStatus is BE's flag showing process of profile status
     * @param status is profile status
     * We always  update @processStatus follow @status when user update
     * But we cannot change UPDATED status, the other team will do it
     * So when @status is UPDATED, we will get it without @processStatus
     */
    if (status === TASKER_ONBOARDING_PROCESS_STATUS.UPDATED) {
        return TASKER_ONBOARDING_PROCESS_STATUS.UPDATED;
    }

    if (
        status === TASKER_ONBOARDING_PROCESS_STATUS.APPROVED &&
        isHaveAppointmentInfo &&
        (!processStatus ||
            processStatus === TASKER_ONBOARDING_PROCESS_STATUS.APPROVED)
    ) {
        return TASKER_ONBOARDING_PROCESS_STATUS.SCHEDULED;
    }

    return processStatus || status;
}

const getTaskerAndPartnerMatcher = ({
    createdAt,
    isPartner,
    status,
}: Pick<GettingProfileProps, 'createdAt' | 'isPartner' | 'status'>) => {
    const statusOriginal = status.filter(
        filteredStatus =>
            filteredStatus === TASKER_ONBOARDING_PROCESS_STATUS.ELIMINATED ||
            filteredStatus === TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE ||
            filteredStatus === TASKER_ONBOARDING_PROCESS_STATUS.UPDATED ||
            filteredStatus === TASKER_ONBOARDING_PROCESS_STATUS.FAIL_UPDATED,
    );
    const matcher: PipelineStage.Match['$match'] = {
        isPartner: { $exists: false },
        updatedAt: {
            $gte: createdAt?.from,
            $lte: createdAt?.to,
        },
        ...(statusOriginal?.length
            ? { $or: [{ status: { $in: statusOriginal } }] }
            : {}),
    };

    if (isPartner) {
        matcher.isPartner = true;
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING)) {
        if (!matcher.$or) matcher.$or = [];

        matcher.$or.push({
            status: { $exists: false },
            processStatus: { $exists: false },
        });
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.REJECTED)) {
        if (!matcher.$or) matcher.$or = [];
        matcher.$or.push({
            $and: [
                { status: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED },
                { processStatus: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED },
            ],
        });
        matcher.$or.push({
            $and: [
                { status: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED },
                { processStatus: { $exists: false } },
            ],
        });
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.APPROVED)) {
        if (!matcher.$or) matcher.$or = [];
        matcher.$or.push({
            $and: [
                { status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED },
                { processStatus: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED },
                { appointmentInfo: { $exists: false } },
            ],
        });
        matcher.$or.push({
            $and: [
                { status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED },
                { processStatus: { $exists: false } },
                { appointmentInfo: { $exists: false } },
            ],
        });
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.RESTORED)) {
        if (!matcher.$or) matcher.$or = [];

        matcher.$or.push({
            $and: [
                { status: { $exists: false } },
                { processStatus: TASKER_ONBOARDING_PROCESS_STATUS.RESTORED },
            ],
        });
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.SCHEDULED)) {
        if (!matcher.$or) matcher.$or = [];

        matcher.$or.push({
            $and: [
                { appointmentInfo: { $exists: true } },
                { processStatus: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED },
            ],
        });
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE)) {
        if (!matcher.$or) matcher.$or = [];

        matcher.$or.push({
            $and: [
                { status: TASKER_PROFILE_STATUS.APPROVED },
                { processStatus: TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE },
            ],
        });
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.ACTIVE)) {
        if (!matcher.$or) matcher.$or = [];

        matcher.$or.push({
            $and: [
                { status: TASKER_PROFILE_STATUS.APPROVED },
                { processStatus: TASKER_ONBOARDING_PROCESS_STATUS.ACTIVE },
            ],
        });
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.FAIL_INTERVIEW)) {
        if (!matcher.$or) matcher.$or = [];

        matcher.$or.push({
            $and: [
                { status: TASKER_PROFILE_STATUS.REJECTED },
                {
                    processStatus:
                        TASKER_ONBOARDING_PROCESS_STATUS.FAIL_INTERVIEW,
                },
            ],
        });
    }

    if (status.includes(TASKER_ONBOARDING_PROCESS_STATUS.FAIL_CALLING)) {
        if (!matcher.$or) matcher.$or = [];

        matcher.$or.push({
            $and: [
                { status: TASKER_PROFILE_STATUS.REJECTED },
                {
                    processStatus:
                        TASKER_ONBOARDING_PROCESS_STATUS.FAIL_CALLING,
                },
            ],
        });
    }

    return matcher;
};

const getEmployeeMatcher = ({
    createdAt,
    status,
}: Pick<GettingProfileProps, 'createdAt' | 'status'>) => {
    const matcher: PipelineStage.Match['$match'] = {
        $or: [{ status: { $in: status } }],
        updatedAt: {
            $gte: createdAt?.from,
            $lte: createdAt?.to,
        },
    };

    if (status?.includes(TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING)) {
        matcher.$or?.push({ status: { $exists: false } });
    }

    return matcher;
};

export async function getTotalTaskerProfile({
    searchText,
    isoCode,
    cities,
    ...restFilterValue
}: GettingProfileProps): Promise<Array<TaskerProfile>['length']> {
    const totalProfile = await taskerProfile()
        .addUpdatedDateField()
        .match(getTaskerAndPartnerMatcher(restFilterValue))
        .lookupAndUnwindUser({ cities, isoCode })
        .search(searchText)
        .count()
        .fetch(isoCode);

    return totalProfile?.[0]?.total || 0;
}

export async function getTaskerProfile({
    searchText,
    sort,
    skip,
    limit,
    isoCode,
    cities,
    ...restFilterValue
}: GettingProfileWithPagination): Promise<
    Array<ReturnResponseFromQuery & TaskerProfile>
> {
    const profiles = await taskerProfile()
        .addUpdatedDateField()
        .match(getTaskerAndPartnerMatcher(restFilterValue))
        .lookupAndUnwindUser({ cities, isoCode })
        .lookupServiceChannel({ isoCode })
        .lookupService({ isoCode })
        .search(searchText || '')
        .sortSkipLimit({
            sort: { ...sort, _id: 1 },
            skip: skip,
            limit: limit,
        })
        .projection()
        .fetch(isoCode);

    return profiles;
}

export async function getTaskerProfileDetail({
    profileId,
    isoCode,
    isPartner,
    cities,
}: {
    profileId: TaskerProfile['_id'];
    isoCode: IsoCode;
    isPartner?: SupplierProfile['isPartner'];
    cities: string[];
}): Promise<{
    data: ReturnResponseFromQuery & TaskerProfile;
}> {
    const profileFound = await taskerProfile()
        .match({
            _id: profileId,
            ...(isPartner
                ? { isPartner: true }
                : { isPartner: { $exists: false } }),
        })
        .lookupAndUnwindUser({ cities, isoCode })
        .lookupServiceChannel({ isoCode })
        .lookupService({ isoCode })
        .projection()
        .fetch(isoCode);

    if (!profileFound?.[0]) {
        throw new Response(null, res404);
    }

    return {
        data: profileFound[0],
    };
}

export async function updateStatusOnTaskerProfile({
    profileId,
    status,
    reason,
    fieldName,
    userId,
    isoCode,
    language,
    username,
    message,
}: UpdatingTaskerProfileProps) {
    const profileFound = await getModels(isoCode)
        .taskerProfile.findById(profileId)
        .select({
            status: 1,
            taskerId: 1,
            processStatus: 1,
            appointmentInfo: 1,
        })
        .lean();

    if (!profileFound) {
        throw new Response(null, res404);
    }

    if (fieldName) {
        const apiURL = getRestApiByMultiRegion({
            apiKey: 'UPDATE_TASKER_PROFILE',
            isoCode,
        });
        const statusKeyFromAPI =
            getImageStatusKeyByImageFieldInTaskerOnboarding({
                imageField: fieldName,
            });

        await fetchAPI(
            apiURL,
            {
                taskerProfileId: profileId,
                [statusKeyFromAPI]: status,
            },
            isoCode,
        );

        await getModels(isoCode).taskerProfile.updateOne(
            { _id: profileId },
            {
                $push: {
                    [`${fieldName}.actionHistories`]: {
                        userId,
                        ...(status === TASKER_PROFILE_STATUS.REJECTED
                            ? { reason }
                            : {}),
                        newStatus: status,
                        updatedByUsername: username,
                        createdAt: momentTz().toDate(),
                    },
                },
            },
        );
    } else {
        const updateBySetCondition: {
            $set: PipelineStage.Set['$set'];
            $unset?: PipelineStage.Set['$set'];
        } = {
            $set: {
                updatedAt: momentTz().toDate(),
            },
        };

        if (
            Object.values(TASKER_PROFILE_STATUS).includes(
                status as TASKER_PROFILE_STATUS,
            )
        ) {
            updateBySetCondition.$set.status = status;
            updateBySetCondition.$set.processStatus = status;
        } else if (status === TASKER_ONBOARDING_PROCESS_STATUS.RESTORED) {
            updateBySetCondition.$unset = {
                status: '',
            };
            updateBySetCondition.$set.processStatus =
                TASKER_ONBOARDING_PROCESS_STATUS.RESTORED;
        } else {
            updateBySetCondition.$set.processStatus = status;

            if (status === TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE)
                updateBySetCondition.$set.status =
                    TASKER_PROFILE_STATUS.APPROVED;

            if (status === TASKER_ONBOARDING_PROCESS_STATUS.ACTIVE)
                updateBySetCondition.$set.status =
                    TASKER_PROFILE_STATUS.APPROVED;

            if (status === TASKER_ONBOARDING_PROCESS_STATUS.FAIL_INTERVIEW)
                updateBySetCondition.$set.status =
                    TASKER_PROFILE_STATUS.REJECTED;

            if (status === TASKER_ONBOARDING_PROCESS_STATUS.FAIL_CALLING)
                updateBySetCondition.$set.status =
                    TASKER_PROFILE_STATUS.REJECTED;
        }

        await getModels(isoCode).taskerProfile.updateOne(
            { _id: profileId },
            {
                ...updateBySetCondition,
                $push: {
                    actionHistories: {
                        userId,
                        oldStatus: getOverallStatusInProfileToServer({
                            processStatus: profileFound?.processStatus,
                            status: profileFound?.status,
                            isHaveAppointmentInfo:
                                !!profileFound?.appointmentInfo,
                        }),
                        newStatus: getOverallStatusInProfileToServer({
                            processStatus:
                                updateBySetCondition.$set.processStatus,
                            status: updateBySetCondition.$set.status,
                            isHaveAppointmentInfo:
                                !!profileFound?.appointmentInfo,
                        }),
                        updatedByUsername: username,
                        ...(reason ? { reason } : {}),
                        createdAt: momentTz().toDate(),
                    },
                },
            },
        );

        if (message) {
            await sendNotification({
                isSendNotificationId: true,
                userIds: [profileFound.taskerId],
                ...(language ? { locale: language } : {}),
                message,
                isoCode,
            });
        }
    }

    return {
        msg: 'UPDATE_PROFILE_SUCCESSFUL',
    };
}

export async function getEmployeeProfileDetail({
    employeeProfileId,
    isoCode,
}: {
    employeeProfileId: string;
    isoCode: IsoCode;
}): Promise<{
    data: ReturnResponseFromQuery &
        StaffProfile & {
            companyInformation: Pick<UserApp, 'name'>;
            taskerName: UserApp['name'];
            taskerPhone: UserApp['phone'];
        };
}> {
    const employeeFound = await employeeProfile()
        .match({ _id: employeeProfileId })
        .lookupAndUnwindUser({ isoCode })
        .lookupServiceChannel({ isoCode })
        .lookupService({ isoCode })
        .lookupAndUnwindCompany({ isoCode })
        .projection()
        .fetch(isoCode);

    if (!employeeFound?.[0]) {
        throw new Response(null, res404);
    }

    return {
        data: employeeFound[0],
    };
}

export async function updateStatusOnEmployeeProfileByAPI({
    profileId,
    status,
    userId,
    fieldName,
    isoCode,
    username,
    reason,
}: UpdatingTaskerProfileProps) {
    const employeeProfileFound = await getModels(
        isoCode,
    ).employeeProfile.findOne({ _id: profileId });

    if (!employeeProfileFound) {
        throw new Response(null, res404);
    }

    const apiURL = getRestApiByMultiRegion({
        apiKey: 'UPDATE_EMPLOYEE_PROFILE',
        isoCode,
    });
    const statusKeyFromAPI = getImageStatusKeyByImageFieldInTaskerOnboarding({
        imageField: fieldName || '',
    });

    await Promise.all([
        fetchAPI(
            apiURL,
            {
                profileId,
                ...(fieldName
                    ? { [statusKeyFromAPI]: status }
                    : { profileStatus: status }),
                ...(reason ? { reason } : {}),
            },
            isoCode,
        ),
        getModels(isoCode).employeeProfile.updateOne(
            { _id: profileId },
            {
                $push: {
                    actionHistories: {
                        userId,
                        ...(fieldName
                            ? { key: fieldName }
                            : {
                                  oldStatus:
                                      employeeProfileFound.status ||
                                      TASKER_PROFILE_STATUS.VERIFYING,
                              }),
                        ...(reason ? { reason } : {}),
                        newStatus: status,
                        updatedByUsername: username,
                        createdAt: momentTz().toDate(),
                    },
                },
            },
        ),
    ]);

    return {
        msg: 'UPDATE_PROFILE_SUCCESSFUL',
    };
}

export async function getTotalEmployeeProfile({
    isoCode,
    searchText,
    ...restFilterValue
}: Omit<GettingProfileProps, 'isPartner'>) {
    const totalProfile = await employeeProfile()
        .addUpdatedDateField()
        .match(getEmployeeMatcher(restFilterValue))
        .lookupAndUnwindUser({ isoCode })
        .search(searchText)
        .count()
        .fetch(isoCode);

    return totalProfile?.[0]?.total || 0;
}

export async function getEmployeeProfile({
    searchText,
    isoCode,
    sort,
    skip,
    limit,
    ...restFilterValue
}: GettingProfileWithPagination): Promise<
    Array<ReturnResponseFromQuery & TaskerProfile>
> {
    const profiles = await employeeProfile()
        .addUpdatedDateField()
        .match(getEmployeeMatcher(restFilterValue))
        .lookupAndUnwindUser({ isoCode })
        .lookupServiceChannel({ isoCode })
        .lookupService({ isoCode })
        .lookupAndUnwindCompany({ isoCode })
        .search(searchText || '')
        .sortSkipLimit({
            skip,
            limit,
        })
        .projection()
        .fetch(isoCode);

    return profiles;
}

// API not support changing status to Rejected, so we will resolve it from BE by this func
export async function rejectEmployeeProfile({
    profileId,
    userId,
    isoCode,
    reason,
    username,
}: Omit<UpdatingTaskerProfileProps, 'language' | 'status'>) {
    const employeeProfileFound = await getModels(
        isoCode,
    ).employeeProfile.findOne({
        _id: profileId,
    });

    if (!employeeProfileFound) {
        throw new Response(null, res404);
    }

    await getModels(isoCode).employeeProfile.updateOne(
        { _id: profileId },
        {
            $set: {
                status: TASKER_PROFILE_STATUS.REJECTED,
            },
            $push: {
                actionHistories: {
                    userId,
                    oldStatus:
                        employeeProfileFound.status ||
                        TASKER_PROFILE_STATUS.VERIFYING,
                    newStatus: TASKER_PROFILE_STATUS.REJECTED,
                    updatedByUsername: username,
                    reason,
                    createdAt: momentTz().toDate(),
                },
            },
        },
    );

    return {
        msg: 'UPDATE_PROFILE_SUCCESSFUL',
    };
}

export async function storeNoteIntoProfile({
    note,
    profileId,
    isoCode,
    userId,
    username,
}: {
    note: NonNullable<TaskerProfile['notes']>[0]['description'];
    profileId: TaskerProfile['_id'];
    isoCode: IsoCode;
    userId: Users['_id'];
    username: Users['username'];
}) {
    const profileFound = await getModels(isoCode)
        .taskerProfile.findById(profileId)
        .lean<TaskerProfile>();

    if (!profileFound?._id) {
        throw new Response(null, res404);
    }

    await getModels(isoCode).taskerProfile.findByIdAndUpdate(profileFound._id, {
        $push: {
            notes: {
                description: note,
                updatedByUserId: userId,
                notedBy: username,
                notedAt: momentTz().toDate(),
            },
        },
    });

    return 'UPDATE_PROFILE_SUCCESSFUL';
}

export async function updateNoteByIndex({
    isoCode,
    profileId,
    note,
    userId,
    username,
    noteIndex,
}: {
    isoCode: IsoCode;
    username: Users['username'];
    userId: Users['_id'];
    noteIndex: number;
    profileId: TaskerProfile['_id'];
    note: NonNullable<TaskerProfile['notes']>[0]['description'];
}) {
    const profileFound = await getModels(isoCode)
        .taskerProfile.findById(profileId)
        .lean();

    if (!profileFound?._id) {
        throw new Response(null, res404);
    }

    await getModels(isoCode).taskerProfile.findByIdAndUpdate(profileId, {
        $set: {
            [`notes.${noteIndex}.description`]: note,
            [`notes.${noteIndex}.notedBy`]: username,
            [`notes.${noteIndex}.updatedByUserId`]: userId,
            [`notes.${noteIndex}.updatedAt`]: momentTz().toDate(),
        },
    });

    return 'UPDATE_PROFILE_SUCCESSFUL';
}

export async function getAllReasonFromSetting({
    isoCode,
}: {
    isoCode: IsoCode;
}) {
    const taskerOnboardingSetting = await getModels(isoCode)
        .taskerOnboardingSetting.findOne({})
        .lean();

    return Array.isArray(taskerOnboardingSetting?.reasons)
        ? taskerOnboardingSetting?.reasons
        : [];
}

export async function resetImageStatusInTaskerOrSupplierProfile({
    isoCode,
    profileId,
    imageField,
    userId,
    reason,
    username,
}: {
    isoCode: IsoCode;
    profileId: TaskerProfile['_id'];
    userId: Users['_id'];
    reason: NonNullable<
        NonNullable<TaskerProfile['identityCard']>['actionHistories']
    >[0]['reason'];
    username: Users['username'];
    imageField: string;
}) {
    await getModels(isoCode).taskerProfile.findOneAndUpdate(
        { _id: profileId },
        {
            $unset: { [`${imageField}.status`]: '' },
            $push: {
                [`${imageField}.actionHistories`]: {
                    userId: userId,
                    updatedByUsername: username,
                    reason,
                    createdAt: momentTz().toDate(),
                },
            },
        },
    );

    return {
        msg: 'RESET_IMAGE_STATUS_IN_TASKER_ONBOARDING_PROFILE_SUCCESSFULLY',
    };
}

/**
 * Update identity verification status in users collection - simplified version
 */
export async function updateIdentityVerificationStatusInUsers({
    userId,
    status,
    reason,
    updatedByUserId,
    updatedByUsername,
    isoCode,
}: {
    userId: string;
    status: string;
    reason?: string;
    updatedByUserId: string;
    updatedByUsername: string;
    isoCode: string;
}) {
    // Get current user data to track old status
    const currentUser = await UsersModel.findById(userId).select({
        identityCard: 1,
    }).lean() as any;

    if (!currentUser) {
        throw new Error(`User not found with ID: ${userId}`);
    }

    const oldStatus = currentUser?.identityCard?.status || IDENTITY_VERIFICATION_STATUS.NOT_UPLOADED;

    // Prepare update data
    const updateData: any = {
        $set: {
            'identityCard.status': status,
            updatedAt: momentTz().toDate(),
        },
        $push: {
            actionHistories: {
                userId: updatedByUserId,
                oldStatus,
                newStatus: status,
                updatedByUsername,
                ...(reason ? { reason } : {}),
                createdAt: momentTz().toDate(),
            },
        },
    };

    // Add reason if rejecting
    if (reason && status === IDENTITY_VERIFICATION_STATUS.NEEDS_UPDATE) {
        updateData.$set['identityCard.reason'] = reason;
    } else if (status === IDENTITY_VERIFICATION_STATUS.APPROVED) {
        // Clear reason when approving
        updateData.$unset = { 'identityCard.reason': '' };
    }

    // Update the user document
    await UsersModel.updateOne({ _id: userId }, updateData);

    return {
        msg: 'UPDATE_IDENTITY_VERIFICATION_SUCCESSFUL',
    };
}

/**
 * Get identity verification data from users collection
 */
export async function getIdentityVerificationFromUsers({
    userId,
}: {
    userId: string;
}) {
    const user = await UsersModel.findById(userId).select({
        identityCard: 1,
        actionHistories: 1,
    }).lean() as any;

    return {
        identityCard: user?.identityCard || null,
        actionHistories: user?.actionHistories || [],
    };
}



export async function getAllOfOffices({ isoCode }: { isoCode: IsoCode }) {
    const offices = await getModels(isoCode)
        .settingSystem.findOne({})
        .lean<SettingSystem>();

    return offices?.submitionAddressForTasker || [];
}

export async function updateScheduleInfoInTaskerOrSupplierProfile({
    isoCode,
    profileId,
    officeInfo,
    date,
    message,
    language,
}: {
    isoCode: EnumIsoCode;
    profileId: TaskerProfile['_id'];
    date: Date;
    officeInfo: Pick<
        TaskerProfile['appointmentInfo'],
        'address' | 'name' | 'city' | 'phoneNumber'
    >;
} & Pick<UpdatingTaskerProfileProps, 'language' | 'message'>) {
    const profileFound = await getModels(isoCode)
        .taskerProfile.findById(profileId)
        .lean();

    if (!profileFound) {
        throw new Error('PROFILE_NOT_FOUND');
    }

    await getModels(isoCode).taskerProfile.findByIdAndUpdate(profileId, {
        $set: {
            appointmentInfo: {
                address: officeInfo.address,
                city: officeInfo.city,
                name: officeInfo.name,
                phoneNumber: officeInfo.phoneNumber,
                date,
            },
            updatedAt: momentTz().toDate(),
        },
    });

    if (message) {
        await sendNotification({
            isSendNotificationId: true,
            userIds: [profileFound.taskerId],
            ...(language ? { locale: language } : {}),
            message,
            isoCode,
        });
    }
}

export async function storeNoteInStaffProfile({
    note,
    profileId,
    isoCode,
    userId,
    username,
}: {
    note: NonNullable<TaskerProfile['notes']>[0]['description'];
    profileId: TaskerProfile['_id'];
    isoCode: IsoCode;
    userId: Users['_id'];
    username: Users['username'];
}) {
    await getModels(isoCode).employeeProfile.findByIdAndUpdate(profileId, {
        $push: {
            notes: {
                description: note,
                updatedByUserId: userId,
                notedBy: username,
                notedAt: momentTz().toDate(),
            },
        },
    });

    return 'UPDATE_PROFILE_SUCCESSFUL';
}

export async function updateNoteInStaffProfileByIndex({
    isoCode,
    profileId,
    note,
    userId,
    username,
    noteIndex,
}: {
    isoCode: IsoCode;
    username: Users['username'];
    userId: Users['_id'];
    noteIndex: number;
    profileId: TaskerProfile['_id'];
    note: NonNullable<TaskerProfile['notes']>[0]['description'];
}) {
    const profileFound = await getModels(isoCode)
        .employeeProfile.findById(profileId)
        .lean();

    if (!profileFound?._id) {
        throw new Response(null, res404);
    }

    await getModels(isoCode).taskerProfile.findByIdAndUpdate(profileId, {
        $set: {
            [`notes.${noteIndex}.description`]: note,
            [`notes.${noteIndex}.notedBy`]: username,
            [`notes.${noteIndex}.updatedByUserId`]: userId,
            [`notes.${noteIndex}.updatedAt`]: momentTz().toDate(),
        },
    });

    return 'UPDATE_PROFILE_SUCCESSFUL';
}
