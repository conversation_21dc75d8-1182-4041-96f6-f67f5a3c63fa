import { json, redirect } from '@remix-run/node';
import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useNavigation,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import type { ConfirmUpdatingProfileFormProps } from 'app/components/tasker-common';
import {
  CardProfileDescription,
  ConfirmUpdatingProfile,
  ConfirmationDescriptions,
  NoteForm,
  ProfileImageAccordion,
  TaskerProfileAction,
  TaskerProfileCard,
  TaskerProfileStatus,
  UpdateNoteDialog,
  UpdateScheduleDialog,
  WarningMissingReasonConfigurationDialog,
} from 'app/components/tasker-common';
import {
  ACTION_NAME,
  IMAGE_PROFILE_TYPE,
  PERMISSIONS,
  REASON_TYPE_IN_TASKER_ONBOARDING_SETTING,
  REASON_UPDATING_IMAGE_IN_TASKER_ONBOARDING_PROFILE,
  ROUTE_NAME,
  TASKER_ONBOARDING_IMAGE_KEY,
  TASKER_ONBOARDING_PROCESS_STATUS,
} from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DataTableBasic,
  DataTableColumnHeader,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Typography,
  toast,
  useBtaskeeFormController,
  useConfirm,
} from 'btaskee-ui';
import type { MappingOverallActionOnProfileDetailProps } from 'btaskee-utils';
import {
  getAllOfTaskerOnboardingMessageLanguage,
  getIsDisableApproveAction,
  getIsDisableSendingNotification,
  getLanguageOption,
  getOverallStatusInProfile,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { ChevronsRight } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hoc404, hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
  getAllOfOffices,
  getAllReasonFromSetting,
  getUserDetailForIdentityVerification,
  updateIdentityVerificationStatusInUsers,
} from '~/services/tasker-profile.server';
import { sendNotification } from '~/services/utils.server';
import { formatPhoneNumber } from '~/utils/common';

export const handle = {
  breadcrumb: ({
    data,
  }: {
    data: SerializeFrom<
      ReturnValueIgnorePromise<typeof getUserDetailForIdentityVerification>
    >['data'];
  }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER}/${data?._id}`}
        label="IDENTITY_INFORMATION_DETAIL"
      />
    );
  },
  i18n: 'identity-information',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const [userDetail, reasonsFromSetting] = await Promise.all([
      hoc404(() =>
        getUserDetailForIdentityVerification({
          userId: params.id || '',
          isoCode,
        }),
      ),
      getAllReasonFromSetting({
        isoCode,
      }),
    ]);

    return json({
      ...userDetail,
      reasonsFromSetting,
    });
  },
  [
    PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
    PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
  ],
);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const tIdentityInformation = await i18next.getFixedT(
      request,
      'identity-information',
    );
    const { isoCode, userId, username } = await getUserSession({
      headers: request.headers,
    });

    const status = formData.get('status')?.toString() || '';
    const reason = formData.get('reason')?.toString() || '';

    // Update identity verification status
    const result = await updateIdentityVerificationStatusInUsers({
      userId: params.id || '',
      status,
      reason,
      updatedByUserId: userId,
      updatedByUsername: username,
      isoCode,
    });

    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_TASKER_PROFILE_STATUS,
      dataRelated: {
        userId: params.id || '',
        newStatus: status,
      },
    });

    return json({ msg: tIdentityInformation(result.msg) });
  },
  [
    PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
    PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
  ],
);

export default function IdentityInformationDetail() {
  const { t: tIdentityInformation } = useTranslation('identity-information');

  const submit = useSubmit();
  const confirm = useConfirm();

  const {
    data: userProfile,
    reasonsFromSetting,
    error: loaderError,
  } = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const [openWarningMissingReasonDialog, setOpenWarningMissingReasonDialog] =
    useState<boolean>(false);

  const reversedActionHistories = useMemo(
    () => userProfile?.actionHistories?.reverse() || [],
    [userProfile?.actionHistories],
  );

  const permissions = useGlobalStore(state => state.permissions);
  const navigation = useNavigation();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const { control, getValues, setValue } =
    useForm<ConfirmUpdatingProfileFormProps>({
      defaultValues: {
        reason: '',
        status: 'VERIFYING',
      },
    });

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  useEffect(() => {
    if (actionData?.msg)
      toast({ description: actionData.msg, variant: 'success' });

    if (actionData?.error) toast({ description: actionData.error });
  }, [actionData]);

  const onApproveIdentity = async () => {
    const isConfirm = await confirm({
      title: tIdentityInformation('APPROVE_IDENTITY'),
      body: tIdentityInformation('CONFIRM_APPROVE_IDENTITY'),
      cancelButton: tIdentityInformation('CANCEL'),
      actionButton: tIdentityInformation('APPROVE'),
    });

    if (isConfirm) {
      const formData = new FormData();
      formData.append('status', 'APPROVED');
      submit(formData, { method: 'post' });
    }
  };

  const onRejectIdentity = async () => {
    const isConfirm = await confirm({
      title: tIdentityInformation('REJECT_IDENTITY'),
      body: (
        <ConfirmUpdatingProfile
          control={control}
          setValue={setValue}
          description={tIdentityInformation('CONFIRM_REJECT_IDENTITY')}
          reasonsFromSetting={reasonsFromSetting}
          taskerInfo={{
            taskerName: userProfile.name,
            username: userProfile.username,
          }}
        />
      ),
      cancelButton: tIdentityInformation('CANCEL'),
      actionButton: tIdentityInformation('REJECT'),
    });

    if (isConfirm) {
      const formData = new FormData();
      const { reason } = getValues();
      formData.append('status', 'NEEDS_UPDATE');
      if (reason) {
        formData.append('reason', reason);
      }
      submit(formData, { method: 'post' });
    }
  };



  const historyColumns: ColumnDef<any>[] = useMemo(
    () => [
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tIdentityInformation('CONTENT_UPDATED')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex gap-3 whitespace-nowrap">
            <Typography variant="p">{row.original?.oldStatus}</Typography>
            <ChevronsRight className="text-primary" />
            <Typography variant="p">{row.original?.newStatus}</Typography>
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedByUsername',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tIdentityInformation('UPDATED_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.updatedByUsername}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tIdentityInformation('DATE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <Typography variant="p">
              {format(row.original?.createdAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
      {
        accessorKey: 'reason',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tIdentityInformation('REASON')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.reason}</Typography>
        ),
      },
    ],
    [tIdentityInformation],
  );



  return (
    <>
      <div className="flex justify-between p-4 align-middle bg-secondary">
        <div className="grid space-y-2 rounded-xl">
          <div className="flex items-center">
            <Typography className="mr-6 capitalize" variant="h2">
              {tIdentityInformation('IDENTITY_INFORMATION_DETAIL')}
            </Typography>
            <Typography variant="p" className="px-3 py-1 text-blue-800 bg-blue-100 rounded">
              {userProfile?.identityCard?.status || 'NOT_UPLOADED'}
            </Typography>
          </div>
          <Breadcrumbs />
        </div>
        <div className="flex gap-6 pr-2 my-auto">
          {(userProfile?.identityCard?.status === 'VERIFYING' || userProfile?.identityCard?.status === 'UPDATED') && (
            <>
              <Button
                onClick={onApproveIdentity}
                disabled={navigation.state === 'submitting'}
                className="bg-green-600 hover:bg-green-700"
              >
                {tIdentityInformation('APPROVE')}
              </Button>
              <Button
                onClick={onRejectIdentity}
                disabled={navigation.state === 'submitting'}
                variant="outline"
                className="text-red-600 border-red-600 hover:bg-red-50"
              >
                {tIdentityInformation('REJECT')}
              </Button>
            </>
          )}
        </div>
      </div>
      <TaskerProfileCard title={tIdentityInformation('USER_INFORMATION')}>
        <CardProfileDescription
          descriptions={[
            {
              label: tIdentityInformation('USER_NAME'),
              value: userProfile?.name,
            },
            {
              label: tIdentityInformation('PHONE_NUMBER'),
              value: formatPhoneNumber(userProfile?.phone),
            },
            {
              label: tIdentityInformation('EMAIL'),
              value: userProfile?.email,
            },
            {
              label: tIdentityInformation('GENDER'),
              value: userProfile?.gender,
            },
            {
              label: tIdentityInformation('CREATED_AT'),
              value: userProfile?.createdAt
                ? format(userProfile.createdAt, 'HH:mm - dd/MM/yyyy')
                : '',
            },
            {
              label: tIdentityInformation('UPDATED_AT'),
              value: userProfile?.updatedAt
                ? format(userProfile.updatedAt, 'HH:mm - dd/MM/yyyy')
                : '',
            },
          ]}
        />
      </TaskerProfileCard>
      <TaskerProfileCard title={tIdentityInformation('IDENTITY_CARD_INFORMATION')}>
        <CardProfileDescription
          descriptions={[
            {
              label: tIdentityInformation('STATUS'),
              value: userProfile?.identityCard?.status || 'NOT_UPLOADED',
            },
            {
              label: tIdentityInformation('UPLOAD_DATE'),
              value: userProfile?.identityCard?.uploadTimestamp
                ? format(userProfile.identityCard.uploadTimestamp, 'HH:mm - dd/MM/yyyy')
                : '',
            },
            {
              label: tIdentityInformation('DETECTED_PHONE'),
              value: userProfile?.identityCard?.detectedPhone || '',
            },
            {
              label: tIdentityInformation('DETECTED_CCCD'),
              value: userProfile?.identityCard?.detectedCCCD || '',
            },
            ...(userProfile?.identityCard?.reason ? [{
              label: tIdentityInformation('REASON'),
              value: userProfile.identityCard.reason,
            }] : []),
          ]}
        />
        {userProfile?.identityCard?.images && userProfile.identityCard.images.length > 0 && (
          <div className="mt-4">
            <Typography variant="h4" className="mb-2">
              {tIdentityInformation('IDENTITY_CARD_IMAGES')}
            </Typography>
            <div className="grid grid-cols-2 gap-4">
              {userProfile.identityCard.images.map((imageUrl, index) => (
                <div key={index} className="overflow-hidden border rounded-lg">
                  <img
                    src={imageUrl}
                    alt={`Identity Card ${index + 1}`}
                    className="object-cover w-full h-48 cursor-pointer hover:opacity-80"
                    onClick={() => window.open(imageUrl, '_blank')}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </TaskerProfileCard>
      <TaskerProfileCard title={tIdentityInformation('ACTION_HISTORY')}>
        <DataTableBasic
          manualPagination
          isDisplayPagination={false}
          columns={historyColumns}
          data={reversedActionHistories}
        />
      </TaskerProfileCard>
      <WarningMissingReasonConfigurationDialog
        openDialog={openWarningMissingReasonDialog}
        taskerName={userProfile?.name}
        setOpenDialog={setOpenWarningMissingReasonDialog}
      />
    </>
  );
}
