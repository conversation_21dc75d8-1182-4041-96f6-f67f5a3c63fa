import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  TaskerProfileTabModel,
} from 'app/components/tasker-common';
import { IDENTITY_INFORMATION_FOR_TASKER_TAB, ROUTE_NAME } from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Grid,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  getPageSizeAndPageIndex,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useIdentityInformation } from '~/hooks/useIdentityInformation';
import { formatPhoneNumber } from '~/utils/common';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function IdentityInformationContent() {
  const { t: tIdentityInformation } = useTranslation('identity-information');

  const [searchParams, setSearchParams] = useSearchParams();

  const {
    permissions,
    tabId,
    status,
    filterValue: filteredValue,
    data: taskerProfiles,
    total: totalProfile,
    userCities,
  } = useIdentityInformation();

  // Add error handling and debugging
  console.log('Identity Information Data:', {
    permissions,
    tabId,
    status,
    filteredValue,
    taskerProfiles,
    totalProfile,
    userCities,
  });

  const navigate = useNavigate();

  const identityInformationColumns: ColumnDef<any>[] = useMemo(
    () => [
      {
        accessorKey: 'phone',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tIdentityInformation('PHONE_NUMBER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatPhoneNumber(row.original?.phone)}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tIdentityInformation('USER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.name}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'email',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tIdentityInformation('EMAIL')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.email}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'gender',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tIdentityInformation('GENDER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {row.original?.gender}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tIdentityInformation('CREATED_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <div className="whitespace-nowrap">
              {format(row.original.createdAt, 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : null,
      },
      {
        accessorKey: 'identityCard.uploadTimestamp',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tIdentityInformation('UPLOAD_DATE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.identityCard?.uploadTimestamp ? (
            <div className="whitespace-nowrap">
              {format(row.original.identityCard.uploadTimestamp, 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : (
            <Typography variant="p" className="text-gray-500">
              {tIdentityInformation('NOT_UPLOADED')}
            </Typography>
          ),
      },
      {
        accessorKey: 'identityCard.status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tIdentityInformation('STATUS')}
          />
        ),
        cell: ({ row }) => {
          const status = row.original?.identityCard?.status || 'NOT_UPLOADED';
          const statusColors = {
            'NOT_UPLOADED': 'bg-gray-100 text-gray-800',
            'VERIFYING': 'bg-blue-100 text-blue-800',
            'APPROVED': 'bg-green-100 text-green-800',
            'NEEDS_UPDATE': 'bg-red-100 text-red-800',
            'UPDATED': 'bg-yellow-100 text-yellow-800',
          };

          return (
            <div className="text-center">
              <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
                {status}
              </Badge>
            </div>
          );
        },
        enableSorting: false,
      },
    ],
    [tIdentityInformation],
  );

  // Show loading or error state if data is not available
  if (!taskerProfiles && totalProfile === undefined) {
    return (
      <div className="flex flex-col gap-6">
        <Grid className="p-4 bg-secondary">
          <div className="grid rounded-xl">
            <Typography className="mb-3 capitalize" variant="h2">
              {tIdentityInformation('IDENTITY_INFORMATION_FOR_TASKER')}
            </Typography>
            <Breadcrumbs />
          </div>
        </Grid>
        <div className="p-4 text-center">
          <Typography variant="p">Loading identity information...</Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      <Grid className="p-4 bg-secondary">
        <div className="grid rounded-xl">
          <Typography className="mb-3 capitalize" variant="h2">
            {tIdentityInformation('IDENTITY_INFORMATION_FOR_TASKER')}
          </Typography>
          <Breadcrumbs />
        </div>
      </Grid>
      <TaskerProfileTabModel
        items={IDENTITY_INFORMATION_FOR_TASKER_TAB}
        permissions={permissions || []}
        value={tabId || ''}
        onValueChange={value => {
          setSearchParams(params => {
            params.set('tabId', value);

            const tabFound = IDENTITY_INFORMATION_FOR_TASKER_TAB.find(
              tab => tab?.tabId === value,
            );

            if (tabFound) {
              params.set('status', tabFound.status.join(','));
            }

            return params;
          });
        }}
      />
      <BTaskeeTable
        total={totalProfile || 0}
        data={taskerProfiles || []}
        columns={identityInformationColumns}
        search={{
          name: 'searchText',
          placeholder: tIdentityInformation('SEARCH_NAME_OR_PHONE'),
          defaultValue: filteredValue?.searchText || '',
        }}
        isShowClearButton
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(filteredValue?.createdAt?.from).toDate(),
            to: momentTz(filteredValue?.createdAt?.to).toDate(),
          },
        }}
        defaultSearchParams={{
          tabId,
        }}
        localeAddress="identity-information"
        onClickRow={profile =>
          navigate(`${ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER}/${profile._id}`)
        }
        pagination={getPageSizeAndPageIndex({
          total: totalProfile || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={[
          ...(status?.length > 1
            ? [
                {
                  placeholder: tIdentityInformation('STATUS'),
                  name: 'status',
                  className: 'w-[129px] h-[42px]',
                  options: status?.map(status => ({
                    label: status,
                    value: status,
                  })),
                  value: filteredValue?.status?.join(','),
                },
              ]
            : []),
          {
            placeholder: tIdentityInformation('CITIES'),
            name: 'cities',
            options:
              userCities?.map(city => ({
                label: city,
                value: city,
              })) || [],
            value: filteredValue?.cities?.join(','),
          },
        ]}
      />
    </div>
  );
}
