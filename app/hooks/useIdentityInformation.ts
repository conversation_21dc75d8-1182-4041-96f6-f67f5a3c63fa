import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    PERMISSIONS,
    TASKER_PROFILE_TAB_ID,
    TASKER_ONBOARDING_PROCESS_STATUS,
    res403,
} from 'btaskee-constants';
import { IDENTITY_VERIFICATION_STATUS } from '~/services/constants.server';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
    getUsersForIdentityVerification,
    getTotalUsersForIdentityVerification,
} from '~/services/tasker-profile.server';

// Status mapping from IDENTITY_VERIFICATION_STATUS to TASKER_ONBOARDING_PROCESS_STATUS
// This is needed for compatibility with existing tab system
const STATUS_MAPPING = {
    [IDENTITY_VERIFICATION_STATUS.NOT_UPLOADED]: TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
    [IDENTITY_VERIFICATION_STATUS.VERIFYING]: TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
    [IDENTITY_VERIFICATION_STATUS.NEEDS_UPDATE]: TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
    [IDENTITY_VERIFICATION_STATUS.UPDATED]: TASKER_ONBOARDING_PROCESS_STATUS.UPDATED,
    [IDENTITY_VERIFICATION_STATUS.APPROVED]: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
};

// Local tab configuration for identity verification system
const IDENTITY_VERIFICATION_TABS = [
    {
        title: 'VERIFY_IDENTITY_INFORMATION_FOR_TASKER',
        tabId: TASKER_PROFILE_TAB_ID.VERIFYING,
        permission: PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
        status: [
            TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
            TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
            TASKER_ONBOARDING_PROCESS_STATUS.UPDATED,
        ],
    },
    {
        title: 'APPROVED_IDENTITY_INFORMATION_FOR_TASKER',
        tabId: TASKER_PROFILE_TAB_ID.APPROVED,
        permission: PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
        status: [TASKER_ONBOARDING_PROCESS_STATUS.APPROVED],
    },
];

export const willBecomeIdentityInformationLoader = async (
    { request }: LoaderFunctionArgs,
    { permissionsPassed }: { permissionsPassed: BtaskeePermissions['key'][] },
) => {
    const { isoCode, isSuperUser, userId } = await getUserSession({
        headers: request.headers,
    });
    const url = new URL(request.url);
    const [
        { tabId, createdAt, status, searchText, sort: sortString, cities },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: [
            'tabId',
            'createdAt',
            'status',
            'searchText',
            'sort',
            'cities',
        ],
        keysNumber: ['pageSize', 'pageIndex'],
    });
    const isManager = await verifyManager(userId);
    const userCities = await getCitiesByUserId({
        userId,
        isManager: isManager || isSuperUser,
    });

    const permissionsFound = [];
    [
        PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
        PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
    ].forEach(permission => {
        if (permissionsPassed.includes(permission)) {
            permissionsFound.push(permission);
        }
    });

    if (!permissionsFound.length) {
        throw new Response(null, res403);
    }

    // Find the tab configuration based on tabId or default to first tab
    const profileFoundByTabId =
        IDENTITY_VERIFICATION_TABS.find(tab => tab.tabId === tabId) ||
        IDENTITY_VERIFICATION_TABS[0];

    // Parse status filter values
    const statusFilterValues = status ? status.split(',') : [];

    // Convert TASKER_ONBOARDING_PROCESS_STATUS to IDENTITY_VERIFICATION_STATUS for backend query
    const convertToIdentityStatus = (status: string) => {
        switch (status) {
            case TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING:
                return [
                    IDENTITY_VERIFICATION_STATUS.NOT_UPLOADED,
                    IDENTITY_VERIFICATION_STATUS.VERIFYING
                ];
            case TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE:
                return [IDENTITY_VERIFICATION_STATUS.NEEDS_UPDATE];
            case TASKER_ONBOARDING_PROCESS_STATUS.UPDATED:
                return [IDENTITY_VERIFICATION_STATUS.UPDATED];
            case TASKER_ONBOARDING_PROCESS_STATUS.APPROVED:
                return [IDENTITY_VERIFICATION_STATUS.APPROVED];
            default:
                return [status];
        }
    };

    const tabStatuses = statusFilterValues?.length
        ? statusFilterValues
        : profileFoundByTabId.status;

    // Convert tab statuses to identity verification statuses
    const identityStatuses = tabStatuses.flatMap(convertToIdentityStatus);

    const filteredValue = {
        status: identityStatuses,
        createdAt: DEFAULT_RANGE_DATE_CURRENT_DAY(createdAt),
        searchText,
        cities: cities ? cities.split(',') : userCities,
    };

    const total = await getTotalUsersForIdentityVerification(filteredValue);

    const { skip, limit } = getSkipAndLimit(
        getPageSizeAndPageIndex({
            total,
            pageSize,
            pageIndex,
        }),
    );

    const sortObj = convertSortString({
        sortString,
        defaultValue: { updatedAt: -1 },
    });

    // Convert sort object to simple Record<string, 1 | -1>
    const simpleSort: Record<string, 1 | -1> = {};
    Object.entries(sortObj).forEach(([key, value]) => {
        if (typeof value === 'number' && (value === 1 || value === -1)) {
            simpleSort[key] = value;
        }
    });

    const rawData = await getUsersForIdentityVerification({
        ...filteredValue,
        skip,
        limit,
        sort: simpleSort,
    });

    // Transform data to match expected format and map identity status to tasker onboarding status
    const data = rawData.map((user: any) => ({
        ...user,
        // Map identity verification status to tasker onboarding status for UI compatibility
        processStatus: STATUS_MAPPING[user.identityCard?.status as keyof typeof STATUS_MAPPING] || TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
        status: STATUS_MAPPING[user.identityCard?.status as keyof typeof STATUS_MAPPING] || TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
    }));

    return json({
        data,
        total,
        filterValue: {
            ...filteredValue,
            status: tabStatuses, // Return the original tab statuses for UI
        },
        tabId: profileFoundByTabId.tabId,
        status: profileFoundByTabId.status,
        userCities,
        permissions: permissionsPassed,
        tabs: IDENTITY_VERIFICATION_TABS,
    });
};

export const useIdentityInformation = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeIdentityInformationLoader>
    >();
};
